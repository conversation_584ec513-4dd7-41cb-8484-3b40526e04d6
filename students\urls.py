from django.urls import path
from . import views

app_name = 'students'

urlpatterns = [
    # Student Management
    path('', views.StudentListView.as_view(), name='list'),
    path('add/', views.StudentCreateView.as_view(), name='add_student'),
    path('<int:pk>/', views.StudentDetailView.as_view(), name='detail'),
    path('<int:pk>/edit/', views.StudentUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.StudentDeleteView.as_view(), name='delete'),
    path('advanced-search/', views.StudentAdvancedSearchView.as_view(), name='advanced_search'),
    path('distribute/', views.DistributeStudentsView.as_view(), name='distribute'),
    path('transfer/', views.StudentTransferView.as_view(), name='transfer'),
    path('cards/', views.StudentCardsView.as_view(), name='cards'),

    # Parent Management
    path('parents/', views.ParentListView.as_view(), name='parent_list'),
    path('parents/add/', views.ParentCreateView.as_view(), name='add_parent'),
    path('parents/<int:pk>/', views.ParentDetailView.as_view(), name='parent_detail'),
    path('parents/<int:pk>/edit/', views.ParentUpdateView.as_view(), name='parent_edit'),
    path('parents/manual/', views.AddParentManualView.as_view(), name='add_parent_manual'),

    # Class Management
    path('classes/', views.ClassListView.as_view(), name='class_list'),
    path('classes/add/', views.ClassCreateView.as_view(), name='class_add'),
    path('classes/<int:pk>/', views.ClassDetailView.as_view(), name='class_detail'),
    path('classes/<int:pk>/edit/', views.ClassUpdateView.as_view(), name='class_edit'),

    # Grade Management
    path('grades/', views.GradeListView.as_view(), name='grade_list'),
    path('grades/add/', views.GradeCreateView.as_view(), name='grade_add'),
    path('grades/<int:pk>/edit/', views.GradeUpdateView.as_view(), name='grade_edit'),

    # Student Affairs
    path('infractions/', views.StudentInfractionsView.as_view(), name='infractions'),
    path('suspension/', views.SuspensionAndBlockView.as_view(), name='suspension'),
    path('vacation-requests/', views.VacationRequestsView.as_view(), name='vacation_requests'),
    path('vacation-requests/approve/', views.ApproveVacationRequestsView.as_view(), name='approve_vacation'),
    path('second-language/', views.StudentSecondLanguageView.as_view(), name='second_language'),

    # Documents and Attachments
    path('documents/', views.StudentDocumentsView.as_view(), name='documents'),
    path('documents/receiver/', views.StudentDocumentReceiverView.as_view(), name='document_receiver'),
    path('attachments/', views.StudentAttachmentsView.as_view(), name='attachments'),

    # Electronic Registration
    path('electronic-registration/', views.ElectronicRegistrationView.as_view(), name='electronic_registration'),

    # Settings
    path('settings/', views.StudentSettingsView.as_view(), name='settings'),
    path('settings/fields/', views.StudentFieldsSettingsView.as_view(), name='fields_settings'),
    path('settings/tabs/', views.StudentTabsSettingsView.as_view(), name='tabs_settings'),
]
