[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "*"
djangorestframework = "*"
django-cors-headers = "*"
django-extensions = "*"
pillow = "*"
celery = "*"
redis = "*"
django-celery-beat = "*"
django-modeltranslation = "*"
django-crispy-forms = "*"
crispy-bootstrap4 = "*"
django-import-export = "*"
django-filter = "*"
django-debug-toolbar = "*"
gunicorn = "*"
psycopg2-binary = "*"
django-redis = "*"

[dev-packages]

[requires]
python_version = "3.12"
